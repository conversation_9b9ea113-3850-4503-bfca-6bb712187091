import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet';
import { motion, AnimatePresence } from 'framer-motion';
import { X, ZoomIn, ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react';

const PhotoGallery = () => {
  const [lightboxImage, setLightboxImage] = useState(null);
  const [currentSlideIndex, setCurrentSlideIndex] = useState({});
  const [isAutoPlaying, setIsAutoPlaying] = useState({});

  // Photo data organized by event sections with additional images
  const eventSections = [
    {
      id: 'brainstorm-2024',
      title: "BrainStorm Cancer Arizona '24",
      images: [
        {
          id: 1,
          src: 'https://static.wixstatic.com/media/7a4df9_0dfdb4b68af6471ea82521e372a4117b~mv2.jpg/v1/fit/w_800,h_600,q_90,enc_avif,quality_auto/7a4df9_0dfdb4b68af6471ea82521e372a4117b~mv2.jpg',
          alt: 'BrainStorm Cancer Arizona 2024 Conference - Group Photo',
          title: 'Conference Group Photo'
        },
        {
          id: 2,
          src: 'https://static.wixstatic.com/media/7a4df9_398c9297428a49f3bbe9a6fa115a0797~mv2.jpg/v1/fit/w_800,h_600,q_90,enc_avif,quality_auto/7a4df9_398c9297428a49f3bbe9a6fa115a0797~mv2.jpg',
          alt: 'BrainStorm Cancer Arizona 2024 Conference - Presentation',
          title: 'Conference Presentation'
        },
        // Additional placeholder images for demonstration
        {
          id: 3,
          src: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop&crop=center',
          alt: 'BrainStorm Cancer Arizona 2024 Conference - Networking Session',
          title: 'Networking Session'
        },
        {
          id: 4,
          src: 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=800&h=600&fit=crop&crop=center',
          alt: 'BrainStorm Cancer Arizona 2024 Conference - Workshop',
          title: 'Educational Workshop'
        }
      ]
    },
    {
      id: 'brainstorm-2025',
      title: "BrainStorm Cancer Arizona '25",
      images: [
        {
          id: 5,
          src: 'https://static.wixstatic.com/media/7a4df9_6de3e3c7ddf0490c81e447b8beba23de~mv2.jpg/v1/fit/w_800,h_600,q_90,enc_avif,quality_auto/7a4df9_6de3e3c7ddf0490c81e447b8beba23de~mv2.jpg',
          alt: 'BrainStorm Cancer Arizona 2025 Conference - Event Photo',
          title: 'Conference Event Photo'
        },
        {
          id: 6,
          src: 'https://static.wixstatic.com/media/7a4df9_287bd0b3e7d94f1293e6676e3c203025~mv2.jpg/v1/fit/w_800,h_600,q_90,enc_avif,quality_auto/7a4df9_287bd0b3e7d94f1293e6676e3c203025~mv2.jpg',
          alt: 'BrainStorm Cancer Arizona 2025 Conference - Networking',
          title: 'Conference Networking'
        },
        {
          id: 7,
          src: 'https://static.wixstatic.com/media/7a4df9_6de3e3c7ddf0490c81e447b8beba23de~mv2.jpg/v1/fit/w_800,h_600,q_90,enc_avif,quality_auto/7a4df9_6de3e3c7ddf0490c81e447b8beba23de~mv2.jpg',
          alt: 'BrainStorm Cancer Arizona 2025 Conference - Additional Photo',
          title: 'Conference Additional Photo'
        },
        // Additional placeholder images for demonstration
        {
          id: 8,
          src: 'https://images.unsplash.com/photo-1582213782179-e0d53f98f2ca?w=800&h=600&fit=crop&crop=center',
          alt: 'BrainStorm Cancer Arizona 2025 Conference - Keynote Speaker',
          title: 'Keynote Speaker'
        },
        {
          id: 9,
          src: 'https://images.unsplash.com/photo-1511578314322-379afb476865?w=800&h=600&fit=crop&crop=center',
          alt: 'BrainStorm Cancer Arizona 2025 Conference - Panel Discussion',
          title: 'Panel Discussion'
        },
        {
          id: 10,
          src: 'https://images.unsplash.com/photo-1515187029135-18ee286d815b?w=800&h=600&fit=crop&crop=center',
          alt: 'BrainStorm Cancer Arizona 2025 Conference - Community Gathering',
          title: 'Community Gathering'
        }
      ]
    }
  ];

  // Initialize slide indices and autoplay states
  useEffect(() => {
    const initialSlideIndex = {};
    const initialAutoPlay = {};

    eventSections.forEach(section => {
      initialSlideIndex[section.id] = 0;
      initialAutoPlay[section.id] = true;
    });

    setCurrentSlideIndex(initialSlideIndex);
    setIsAutoPlaying(initialAutoPlay);
  }, []);

  // Auto-advance slides
  useEffect(() => {
    const intervals = {};

    eventSections.forEach(section => {
      if (isAutoPlaying[section.id] && section.images.length > 1) {
        intervals[section.id] = setInterval(() => {
          setCurrentSlideIndex(prev => ({
            ...prev,
            [section.id]: (prev[section.id] + 1) % section.images.length
          }));
        }, 4000); // Change slide every 4 seconds
      }
    });

    return () => {
      Object.values(intervals).forEach(interval => clearInterval(interval));
    };
  }, [isAutoPlaying, eventSections]);

  const nextSlide = (sectionId) => {
    const section = eventSections.find(s => s.id === sectionId);
    setCurrentSlideIndex(prev => ({
      ...prev,
      [sectionId]: (prev[sectionId] + 1) % section.images.length
    }));
  };

  const prevSlide = (sectionId) => {
    const section = eventSections.find(s => s.id === sectionId);
    setCurrentSlideIndex(prev => ({
      ...prev,
      [sectionId]: prev[sectionId] === 0 ? section.images.length - 1 : prev[sectionId] - 1
    }));
  };

  const toggleAutoPlay = (sectionId) => {
    setIsAutoPlaying(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  const goToSlide = (sectionId, index) => {
    setCurrentSlideIndex(prev => ({
      ...prev,
      [sectionId]: index
    }));
  };

  const openLightbox = (image) => {
    setLightboxImage(image);
  };

  const closeLightbox = () => {
    setLightboxImage(null);
  };

  return (
    <>
      <Helmet>
        <title>Photo Gallery - Living Oncology</title>
        <meta name="description" content="Explore photos from Living Oncology events, conferences, workshops, and community gatherings. See our impact in the neuro-oncology community." />
      </Helmet>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 to-yellow-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold text-primary mb-6">
              Photo Gallery
            </h1>
            <p className="text-xl md:text-2xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
              Capturing moments of hope, learning, and community in our journey together
            </p>
          </motion.div>
        </div>
      </section>

      {/* Event Sections with Slideshows */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {eventSections.map((section, sectionIndex) => (
            <motion.div
              key={section.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: sectionIndex * 0.2 }}
              viewport={{ once: true }}
              className="mb-20 last:mb-0"
            >
              {/* Section Title */}
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">
                  {section.title}
                </h2>
                <div className="w-24 h-1 bg-accent mx-auto"></div>
              </div>

              {/* Slideshow Container */}
              <div className="relative max-w-4xl mx-auto">
                <div className="relative aspect-[16/10] bg-gray-100 rounded-xl overflow-hidden shadow-2xl">
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={`${section.id}-${currentSlideIndex[section.id]}`}
                      initial={{ opacity: 0, x: 300 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -300 }}
                      transition={{ duration: 0.5, ease: "easeInOut" }}
                      className="absolute inset-0 cursor-pointer"
                      onClick={() => openLightbox(section.images[currentSlideIndex[section.id] || 0])}
                    >
                      <img
                        src={section.images[currentSlideIndex[section.id] || 0]?.src}
                        alt={section.images[currentSlideIndex[section.id] || 0]?.alt}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                        <ZoomIn className="w-12 h-12 text-white opacity-0 hover:opacity-100 transition-opacity duration-300" />
                      </div>
                    </motion.div>
                  </AnimatePresence>

                  {/* Navigation Arrows */}
                  {section.images.length > 1 && (
                    <>
                      <button
                        onClick={() => prevSlide(section.id)}
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 hover:bg-opacity-75 text-white p-3 rounded-full transition-all duration-300 z-10"
                        aria-label="Previous image"
                      >
                        <ChevronLeft className="w-6 h-6" />
                      </button>
                      <button
                        onClick={() => nextSlide(section.id)}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 hover:bg-opacity-75 text-white p-3 rounded-full transition-all duration-300 z-10"
                        aria-label="Next image"
                      >
                        <ChevronRight className="w-6 h-6" />
                      </button>
                    </>
                  )}

                  {/* Play/Pause Button */}
                  {section.images.length > 1 && (
                    <button
                      onClick={() => toggleAutoPlay(section.id)}
                      className="absolute top-4 right-4 bg-black bg-opacity-50 hover:bg-opacity-75 text-white p-3 rounded-full transition-all duration-300 z-10"
                      aria-label={isAutoPlaying[section.id] ? "Pause slideshow" : "Play slideshow"}
                    >
                      {isAutoPlaying[section.id] ? (
                        <Pause className="w-5 h-5" />
                      ) : (
                        <Play className="w-5 h-5" />
                      )}
                    </button>
                  )}

                  {/* Image Title Overlay */}
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6">
                    <h3 className="text-white text-xl font-semibold">
                      {section.images[currentSlideIndex[section.id] || 0]?.title}
                    </h3>
                  </div>
                </div>

                {/* Thumbnail Navigation */}
                {section.images.length > 1 && (
                  <div className="flex justify-center mt-6 space-x-2 overflow-x-auto pb-2">
                    {section.images.map((image, index) => (
                      <button
                        key={image.id}
                        onClick={() => goToSlide(section.id, index)}
                        className={`flex-shrink-0 w-20 h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                          (currentSlideIndex[section.id] || 0) === index
                            ? 'border-primary shadow-lg scale-110'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        <img
                          src={image.src}
                          alt={image.alt}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>
                )}

                {/* Slide Counter */}
                {section.images.length > 1 && (
                  <div className="text-center mt-4">
                    <span className="text-gray-600 text-sm">
                      {(currentSlideIndex[section.id] || 0) + 1} of {section.images.length}
                    </span>
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      </section>

      {/* Lightbox Modal */}
      <AnimatePresence>
        {lightboxImage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center p-4"
            onClick={closeLightbox}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="relative max-w-5xl max-h-full"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={closeLightbox}
                className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors z-10"
                aria-label="Close lightbox"
              >
                <X className="w-8 h-8" />
              </button>

              <div className="bg-white rounded-lg overflow-hidden shadow-2xl">
                <img
                  className="w-full h-auto max-h-[80vh] object-contain"
                  src={lightboxImage.src}
                  alt={lightboxImage.alt}
                />
                {lightboxImage.title && (
                  <div className="p-4 bg-white">
                    <h3 className="text-lg font-semibold text-primary text-center">
                      {lightboxImage.title}
                    </h3>
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-green-600 to-green-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold text-white mb-6">
              Join Our Community
            </h2>
            <p className="text-xl text-green-100 mb-8 max-w-3xl mx-auto">
              Be part of our growing community and help us create more moments of hope, learning, and connection.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a href="/brainstorm-cancer" className="bg-white text-green-700 hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                View Our Events
              </a>
              <a href="/contact" className="btn-primary">
                Get Involved
              </a>
            </div>
          </motion.div>
        </div>
      </section>
    </>
  );
};

export default PhotoGallery;