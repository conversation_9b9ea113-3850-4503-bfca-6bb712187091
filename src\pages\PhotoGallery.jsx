import React, { useState } from 'react';
import { Helmet } from 'react-helmet';
import { motion, AnimatePresence } from 'framer-motion';
import { X, ZoomIn } from 'lucide-react';

const PhotoGallery = () => {
  const [lightboxImage, setLightboxImage] = useState(null);

  // Photo data organized by event sections
  const eventSections = [
    {
      id: 'brainstorm-2024',
      title: "BrainStorm Cancer Arizona '24",
      images: [
        {
          id: 1,
          src: 'https://static.wixstatic.com/media/7a4df9_0dfdb4b68af6471ea82521e372a4117b~mv2.jpg/v1/fit/w_696,h_464,q_90,enc_avif,quality_auto/7a4df9_0dfdb4b68af6471ea82521e372a4117b~mv2.jpg',
          alt: 'BrainStorm Cancer Arizona 2024 Conference - Group Photo',
          title: 'Conference Group Photo'
        },
        {
          id: 2,
          src: 'https://static.wixstatic.com/media/7a4df9_398c9297428a49f3bbe9a6fa115a0797~mv2.jpg/v1/fit/w_696,h_464,q_90,enc_avif,quality_auto/7a4df9_398c9297428a49f3bbe9a6fa115a0797~mv2.jpg',
          alt: 'BrainStorm Cancer Arizona 2024 Conference - Presentation',
          title: 'Conference Presentation'
        }
      ]
    },
    {
      id: 'brainstorm-2025',
      title: "BrainStorm Cancer Arizona '25",
      images: [
        {
          id: 3,
          src: 'https://static.wixstatic.com/media/7a4df9_6de3e3c7ddf0490c81e447b8beba23de~mv2.jpg/v1/fit/w_591,h_464,q_90,enc_avif,quality_auto/7a4df9_6de3e3c7ddf0490c81e447b8beba23de~mv2.jpg',
          alt: 'BrainStorm Cancer Arizona 2025 Conference - Event Photo',
          title: 'Conference Event Photo'
        },
        {
          id: 4,
          src: 'https://static.wixstatic.com/media/7a4df9_287bd0b3e7d94f1293e6676e3c203025~mv2.jpg/v1/fit/w_591,h_464,q_90,enc_avif,quality_auto/7a4df9_287bd0b3e7d94f1293e6676e3c203025~mv2.jpg',
          alt: 'BrainStorm Cancer Arizona 2025 Conference - Networking',
          title: 'Conference Networking'
        },
        {
          id: 5,
          src: 'https://static.wixstatic.com/media/7a4df9_6de3e3c7ddf0490c81e447b8beba23de~mv2.jpg/v1/fit/w_591,h_464,q_90,enc_avif,quality_auto/7a4df9_6de3e3c7ddf0490c81e447b8beba23de~mv2.jpg',
          alt: 'BrainStorm Cancer Arizona 2025 Conference - Additional Photo',
          title: 'Conference Additional Photo'
        }
      ]
    }
  ];

  const openLightbox = (image) => {
    setLightboxImage(image);
  };

  const closeLightbox = () => {
    setLightboxImage(null);
  };

  return (
    <>
      <Helmet>
        <title>Photo Gallery - Living Oncology</title>
        <meta name="description" content="Explore photos from Living Oncology events, conferences, workshops, and community gatherings. See our impact in the neuro-oncology community." />
      </Helmet>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 to-yellow-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold text-primary mb-6">
              Photo Gallery
            </h1>
            <p className="text-xl md:text-2xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
              Capturing moments of hope, learning, and community in our journey together
            </p>
          </motion.div>
        </div>
      </section>

      {/* Event Sections */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {eventSections.map((section, sectionIndex) => (
            <motion.div
              key={section.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: sectionIndex * 0.2 }}
              viewport={{ once: true }}
              className="mb-16 last:mb-0"
            >
              {/* Section Title */}
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">
                  {section.title}
                </h2>
                <div className="w-24 h-1 bg-accent mx-auto"></div>
              </div>

              {/* Photo Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {section.images.map((image, index) => (
                  <motion.div
                    key={image.id}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="group relative bg-gray-100 rounded-xl overflow-hidden shadow-lg card-hover cursor-pointer"
                    onClick={() => openLightbox(image)}
                  >
                    <div className="aspect-[4/3] relative">
                      <img
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                        src={image.src}
                        alt={image.alt}
                        loading="lazy"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                        <ZoomIn className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>
      </section>

      {/* Lightbox Modal */}
      <AnimatePresence>
        {lightboxImage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center p-4"
            onClick={closeLightbox}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="relative max-w-5xl max-h-full"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={closeLightbox}
                className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors z-10"
                aria-label="Close lightbox"
              >
                <X className="w-8 h-8" />
              </button>

              <div className="bg-white rounded-lg overflow-hidden shadow-2xl">
                <img
                  className="w-full h-auto max-h-[80vh] object-contain"
                  src={lightboxImage.src}
                  alt={lightboxImage.alt}
                />
                {lightboxImage.title && (
                  <div className="p-4 bg-white">
                    <h3 className="text-lg font-semibold text-primary text-center">
                      {lightboxImage.title}
                    </h3>
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-green-600 to-green-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold text-white mb-6">
              Join Our Community
            </h2>
            <p className="text-xl text-green-100 mb-8 max-w-3xl mx-auto">
              Be part of our growing community and help us create more moments of hope, learning, and connection.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a href="/brainstorm-cancer" className="bg-white text-green-700 hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                View Our Events
              </a>
              <a href="/contact" className="btn-primary">
                Get Involved
              </a>
            </div>
          </motion.div>
        </div>
      </section>
    </>
  );
};

export default PhotoGallery;