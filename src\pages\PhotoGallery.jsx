import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet';
import { motion, AnimatePresence } from 'framer-motion';
import { X, ZoomIn, ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react';

const PhotoGallery = () => {
  const [lightboxImage, setLightboxImage] = useState(null);
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // All photos combined into one slideshow
  const allImages = [
    // BrainStorm Cancer Arizona '24 images
    {
      id: 1,
      src: 'https://static.wixstatic.com/media/7a4df9_0dfdb4b68af6471ea82521e372a4117b~mv2.jpg/v1/fit/w_800,h_600,q_90,enc_avif,quality_auto/7a4df9_0dfdb4b68af6471ea82521e372a4117b~mv2.jpg',
      alt: 'BrainStorm Cancer Arizona 2024 Conference - Group Photo',
      title: "BrainStorm Cancer Arizona '24 - Conference Group Photo",
      event: "BrainStorm Cancer Arizona '24"
    },
    {
      id: 2,
      src: 'https://static.wixstatic.com/media/7a4df9_398c9297428a49f3bbe9a6fa115a0797~mv2.jpg/v1/fit/w_800,h_600,q_90,enc_avif,quality_auto/7a4df9_398c9297428a49f3bbe9a6fa115a0797~mv2.jpg',
      alt: 'BrainStorm Cancer Arizona 2024 Conference - Presentation',
      title: "BrainStorm Cancer Arizona '24 - Conference Presentation",
      event: "BrainStorm Cancer Arizona '24"
    },
    // BrainStorm Cancer Arizona '25 images
    {
      id: 3,
      src: 'https://static.wixstatic.com/media/7a4df9_6de3e3c7ddf0490c81e447b8beba23de~mv2.jpg/v1/fit/w_800,h_600,q_90,enc_avif,quality_auto/7a4df9_6de3e3c7ddf0490c81e447b8beba23de~mv2.jpg',
      alt: 'BrainStorm Cancer Arizona 2025 Conference - Event Photo',
      title: "BrainStorm Cancer Arizona '25 - Conference Event Photo",
      event: "BrainStorm Cancer Arizona '25"
    },
    {
      id: 4,
      src: 'https://static.wixstatic.com/media/7a4df9_287bd0b3e7d94f1293e6676e3c203025~mv2.jpg/v1/fit/w_800,h_600,q_90,enc_avif,quality_auto/7a4df9_287bd0b3e7d94f1293e6676e3c203025~mv2.jpg',
      alt: 'BrainStorm Cancer Arizona 2025 Conference - Networking',
      title: "BrainStorm Cancer Arizona '25 - Conference Networking",
      event: "BrainStorm Cancer Arizona '25"
    },
    {
      id: 5,
      src: 'https://static.wixstatic.com/media/7a4df9_6de3e3c7ddf0490c81e447b8beba23de~mv2.jpg/v1/fit/w_800,h_600,q_90,enc_avif,quality_auto/7a4df9_6de3e3c7ddf0490c81e447b8beba23de~mv2.jpg',
      alt: 'BrainStorm Cancer Arizona 2025 Conference - Additional Photo',
      title: "BrainStorm Cancer Arizona '25 - Conference Additional Photo",
      event: "BrainStorm Cancer Arizona '25"
    },
    // Additional event photos
    {
      id: 6,
      src: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop&crop=center',
      alt: 'BrainStorm Cancer Conference - Networking Session',
      title: 'BrainStorm Cancer - Networking Session',
      event: 'Community Events'
    },
    {
      id: 7,
      src: 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=800&h=600&fit=crop&crop=center',
      alt: 'BrainStorm Cancer Conference - Educational Workshop',
      title: 'BrainStorm Cancer - Educational Workshop',
      event: 'Community Events'
    },
    {
      id: 8,
      src: 'https://images.unsplash.com/photo-1582213782179-e0d53f98f2ca?w=800&h=600&fit=crop&crop=center',
      alt: 'BrainStorm Cancer Conference - Keynote Speaker',
      title: 'BrainStorm Cancer - Keynote Speaker',
      event: 'Community Events'
    }
  ];

  // Auto-advance slides
  useEffect(() => {
    if (isAutoPlaying && allImages.length > 1) {
      const interval = setInterval(() => {
        setCurrentSlideIndex(prev => (prev + 1) % allImages.length);
      }, 4000); // Change slide every 4 seconds

      return () => clearInterval(interval);
    }
  }, [isAutoPlaying, allImages.length]);

  const nextSlide = () => {
    setCurrentSlideIndex(prev => (prev + 1) % allImages.length);
  };

  const prevSlide = () => {
    setCurrentSlideIndex(prev => prev === 0 ? allImages.length - 1 : prev - 1);
  };

  const toggleAutoPlay = () => {
    setIsAutoPlaying(prev => !prev);
  };

  const goToSlide = (index) => {
    setCurrentSlideIndex(index);
  };

  const openLightbox = (image) => {
    setLightboxImage(image);
  };

  const closeLightbox = () => {
    setLightboxImage(null);
  };

  return (
    <>
      <Helmet>
        <title>Photo Gallery - Living Oncology</title>
        <meta name="description" content="Explore photos from Living Oncology events, conferences, workshops, and community gatherings. See our impact in the neuro-oncology community." />
      </Helmet>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 to-yellow-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold text-primary mb-6">
              Photo Gallery
            </h1>
            <p className="text-xl md:text-2xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
              Capturing moments of hope, learning, and community in our journey together
            </p>
          </motion.div>
        </div>
      </section>

      {/* Single Photo Slideshow */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {/* Slideshow Container */}
            <div className="relative max-w-5xl mx-auto">
              <div className="relative aspect-[16/10] bg-gray-100 rounded-xl overflow-hidden shadow-2xl">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={currentSlideIndex}
                    initial={{ opacity: 0, x: 300 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -300 }}
                    transition={{ duration: 0.5, ease: "easeInOut" }}
                    className="absolute inset-0 cursor-pointer"
                    onClick={() => openLightbox(allImages[currentSlideIndex])}
                  >
                    <img
                      src={allImages[currentSlideIndex]?.src}
                      alt={allImages[currentSlideIndex]?.alt}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                      <ZoomIn className="w-12 h-12 text-white opacity-0 hover:opacity-100 transition-opacity duration-300" />
                    </div>
                  </motion.div>
                </AnimatePresence>

                {/* Navigation Arrows */}
                {allImages.length > 1 && (
                  <>
                    <button
                      onClick={prevSlide}
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 hover:bg-opacity-75 text-white p-3 rounded-full transition-all duration-300 z-10"
                      aria-label="Previous image"
                    >
                      <ChevronLeft className="w-6 h-6" />
                    </button>
                    <button
                      onClick={nextSlide}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 hover:bg-opacity-75 text-white p-3 rounded-full transition-all duration-300 z-10"
                      aria-label="Next image"
                    >
                      <ChevronRight className="w-6 h-6" />
                    </button>
                  </>
                )}

                {/* Play/Pause Button */}
                {allImages.length > 1 && (
                  <button
                    onClick={toggleAutoPlay}
                    className="absolute top-4 right-4 bg-black bg-opacity-50 hover:bg-opacity-75 text-white p-3 rounded-full transition-all duration-300 z-10"
                    aria-label={isAutoPlaying ? "Pause slideshow" : "Play slideshow"}
                  >
                    {isAutoPlaying ? (
                      <Pause className="w-5 h-5" />
                    ) : (
                      <Play className="w-5 h-5" />
                    )}
                  </button>
                )}

                {/* Image Title and Event Overlay */}
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6">
                  <div className="text-white">
                    <p className="text-sm opacity-80 mb-1">{allImages[currentSlideIndex]?.event}</p>
                    <h3 className="text-xl font-semibold">
                      {allImages[currentSlideIndex]?.title}
                    </h3>
                  </div>
                </div>
              </div>

              {/* Thumbnail Navigation */}
              {allImages.length > 1 && (
                <div className="flex justify-center mt-6 space-x-2 overflow-x-auto pb-2">
                  {allImages.map((image, index) => (
                    <button
                      key={image.id}
                      onClick={() => goToSlide(index)}
                      className={`flex-shrink-0 w-20 h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                        currentSlideIndex === index
                          ? 'border-primary shadow-lg scale-110'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      <img
                        src={image.src}
                        alt={image.alt}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              )}

              {/* Slide Counter */}
              {allImages.length > 1 && (
                <div className="text-center mt-4">
                  <span className="text-gray-600 text-sm">
                    {currentSlideIndex + 1} of {allImages.length}
                  </span>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Lightbox Modal */}
      <AnimatePresence>
        {lightboxImage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center p-4"
            onClick={closeLightbox}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="relative max-w-5xl max-h-full"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={closeLightbox}
                className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors z-10"
                aria-label="Close lightbox"
              >
                <X className="w-8 h-8" />
              </button>

              <div className="bg-white rounded-lg overflow-hidden shadow-2xl">
                <img
                  className="w-full h-auto max-h-[80vh] object-contain"
                  src={lightboxImage.src}
                  alt={lightboxImage.alt}
                />
                {lightboxImage.title && (
                  <div className="p-4 bg-white">
                    <h3 className="text-lg font-semibold text-primary text-center">
                      {lightboxImage.title}
                    </h3>
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-green-600 to-green-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold text-white mb-6">
              Join Our Community
            </h2>
            <p className="text-xl text-green-100 mb-8 max-w-3xl mx-auto">
              Be part of our growing community and help us create more moments of hope, learning, and connection.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a href="/brainstorm-cancer" className="bg-white text-green-700 hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                View Our Events
              </a>
              <a href="/contact" className="btn-primary">
                Get Involved
              </a>
            </div>
          </motion.div>
        </div>
      </section>
    </>
  );
};

export default PhotoGallery;